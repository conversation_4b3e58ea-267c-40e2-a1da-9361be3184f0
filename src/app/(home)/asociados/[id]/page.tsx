"use server";
import ReadIndexRenderer from "@/_core/ui/components/CRUD/readIndex/readIndexRenderer";
// import EstadoUpdaterButton from "@/_core/ui/components/button/estadoUpdaterButton";
import HistoricalAccordion from "@/_core/ui/components/acordion/historicalAcordion";
import { Breadcrumbs, Stack, Typography } from "@mui/material";
import Link from "next/link";
import EditableTab from "@/_core/ui/components/tab/editableTab";
import ReadCollectionRenderer from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";
import { ColumnConfigWithStringRenderer } from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";

import { runFormAction } from "@/_lib/data/model/action/actionFactory";

const ENTITY = "asociados";
const ACTION = "index";

export default async function AsociadoPage( { params }: { params: { id: string } } ) {
  const { id } = await params;

  const columnsConfigCuenta = [
    { key: "fecha_pago", label: "Fecha pago", renderer: "default" },
    { key: "concepto", label: "Concepto", renderer: "default" },
    { key: "importe", label: "Importe", renderer: "currency" },
    { key: "estado", label: "Estado", renderer: "badge", options: {
      bg: "bg-gray-100",
      text: "text-gray-700",
    } },
    { key: "__actions", label: "", renderer: "actions" },
  ] as ColumnConfigWithStringRenderer[];

  const columnsConfigTramites = [
    { key: "tramite", label: "Trámite", renderer: "default" },
    { key: "numero", label: "Número", renderer: "integer" },
    { key: "fecha", label: "Fecha", renderer: "default" },
    { key: "fecha_vencimiento", label: "Fecha Vto.", renderer: "default" },
  ] as ColumnConfigWithStringRenderer[];

  const columnsConfigSuscripciones = [
    { key: "fecha_desde", label: "Fecha desde", renderer: "default" },
    { key: "fecha_hasta", label: "Fecha hasta", renderer: "default" },
    { key: "codigo", label: "Código", renderer: "default" },
    { key: "retención", label: "Retención", renderer: "currency" },
    { key: "valor", label: "Valor", renderer: "currency" },
  ] as ColumnConfigWithStringRenderer[];
  
  const data = await runFormAction(ENTITY, ACTION, {
    params: {
      id: id,
    },
  }) as Record<string, unknown>;

  const dataMatriculado = {
    nombreApellido: `${typeof data.nombre === "string" ? data.nombre.charAt(0).toUpperCase() + data.nombre.slice(1) : ""} ${typeof data.apellido === "string" ? data.apellido.charAt(0).toUpperCase() + data.apellido.slice(1) : ""}`,
    fechaNacimiento: data.fechaNacimiento as string,
    dni: data.dni as number,
    matricula: Array.isArray(data.matriculas) ? data.matriculas[0] : {},
    domicilio: Array.isArray(data.domicilios) ? data.domicilios[0] : {},
    vinculacion: Array.isArray(data.vinculaciones) ? data.vinculaciones[0] : {},

  }
  

  return (
    <div className="p-6 space-y-4">
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb">
        <Link className="hover:underline" href="/dashboard" color="inherit">
          Home
        </Link>
        <Link className="hover:underline" href="/asociados" color="inherit">
          Asociados
        </Link>
        <Typography color="inherit" className="pointer-events-none select-none">
          {dataMatriculado.nombreApellido}
        </Typography>
      </Breadcrumbs>

      {/* Detalle del asociado */}
      <ReadIndexRenderer
        title={`${dataMatriculado.nombreApellido}`}
        subtitle={`DNI: ${dataMatriculado.dni}`} 
        badges={[
          {
            label: dataMatriculado.matricula.estadoActual?.estado?.nombre ?? "",
            bgColor: "#F2F4F8",
            textColor: "contrastText.main",
          },
        ]}
        actions={<></>}
        tabs={[
          {
            label: "Datos personales",
            content: (
              <Stack direction="column" spacing={2}>
                <EditableTab>
                  <div className="text-sm space-y-2">
                    <Typography variant="body1" color="text.secondary">
                      <strong>NOMBRE Y APELLIDO:</strong> {dataMatriculado.nombreApellido}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      <strong>FECHA NACIMIENTO:</strong> {dataMatriculado.fechaNacimiento.split("T")[0]}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      <strong>DNI:</strong> {dataMatriculado.dni}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      <strong>CUIT:</strong> -
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      <strong>FECHA DE ALTA:</strong> {dataMatriculado.matricula.fechaAlta}
                    </Typography>

                    <HistoricalAccordion
                      title="Historial"
                      items={[
                        { label: "dd/mm/aaaa", value: "BAJA" },
                        { label: "MOTIVO:", value: "Fallecimiento" },
                        { label: "dd/mm/aaaa", value: "ACTIVO" },
                      ]}
                    />
                  </div>
                </EditableTab>
                <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Matrícula
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>MAT. PROVINCIAL</strong> {dataMatriculado.matricula.numero}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>FECHA VTO:</strong> {dataMatriculado.matricula.fechaVencimiento}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>CATEGORIA:</strong> {dataMatriculado.matricula.formacion.categoria.nombre}
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Título
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>FECHA TITULO:</strong> {dataMatriculado.matricula.formacion.fechaEmisionTitulo.split("T")[0]}
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack>
                {/* <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Seguro de mala praxis
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>VTO. SEGURO MALA PRAXIS:</strong> dd/mm/aaaa
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Superintendencia Salud
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>N ° INSC. SSSALUD:</strong> -
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>VTO. INSC. SSSALUD:</strong> dd/mm/aaaa
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack> */}
                <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Domicilio Particular
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>CALLE:</strong> {dataMatriculado.domicilio.calle}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>NÚMERO:</strong> {dataMatriculado.domicilio.numero}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>PISO:</strong> {dataMatriculado.domicilio.piso}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>DPTO:</strong> {dataMatriculado.domicilio.depto}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>LOCALIDAD:</strong> {dataMatriculado.domicilio.localidad?.nombre}
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Contacto
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>TELÉFONO:</strong> {dataMatriculado.domicilio.telefonoCodigoArea}-{dataMatriculado.domicilio.telefonoNumero}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>E-MAIL:</strong>{dataMatriculado.domicilio.email}
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack>
              </Stack>
            ),
          },
          {
            label: "Laboral",
            content: (
              <Stack direction="column" spacing={2}>
                <EditableTab>
                  <div className="text-sm space-y-2">
                    <Stack direction="row" spacing={2}>
                      <Typography variant="body1" color="text.secondary">
                        <strong>RAZÓN SOCIAL:</strong>
                      </Typography>
                      <Link href={`/laboratorios/${dataMatriculado.vinculacion?.laboratorio?.id}`}>
                        <Typography variant="body1" color="link.main">
                          {dataMatriculado.vinculacion?.laboratorio?.razonSocial}
                        </Typography>
                      </Link>
                    </Stack>
                    <Typography variant="body1" color="text.secondary">
                      <strong>SUCURSAL:</strong>
                    </Typography>
                  </div>
                </EditableTab>
                <Stack direction="row" spacing={2}>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Domicilio
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>CALLE:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>NÚMERO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>PISO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>DPTO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>LOCALIDAD:</strong>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                  <Stack direction={"column"} spacing={2} width={"50%"}>
                    <Typography variant="h6xl" color="text.secondary">
                      Contacto
                    </Typography>
                    <EditableTab>
                      <div className="text-sm space-y-2">
                        <Typography variant="body1" color="text.secondary">
                          <strong>TELÉFONO:</strong>
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          <strong>E-MAIL:</strong>
                        </Typography>
                      </div>
                    </EditableTab>
                  </Stack>
                </Stack>
              </Stack>
            ),
          },
          {
            label: "Cuenta",
            content:(
              <Stack direction="column" spacing={2}>
                <ReadCollectionRenderer
                  entityName="asociados"
                  actionName="cuenta"
                  columnsConfig={columnsConfigCuenta}
                  variant="tab"
                />
              </Stack>
            ),
          },
          {
            label: "Configuraciones",
            content: (
              <Stack direction="column" spacing={2}>
                <Typography variant="h6xl" color="text.secondary">
                  Suscripciones
                </Typography>
                <ReadCollectionRenderer
                  entityName="asociados"
                  actionName="suscripciones"
                  columnsConfig={columnsConfigSuscripciones}
                  variant="tab"
                />
              </Stack>
            ),
          },
          {
            label: "Trámites",
            content: (
              <Stack direction="column" spacing={2}>
                <ReadCollectionRenderer
                  entityName="asociados"
                  actionName="tramites"
                  columnsConfig={columnsConfigTramites}
                  variant="tab"
                />
              </Stack>
            ),
          },
        ]}
      />
    </div>
  );
}
